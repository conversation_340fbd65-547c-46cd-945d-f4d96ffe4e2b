### 测试新增的机构列表查询接口（不分页）

### 1. 查询所有机构列表（不分页）
GET {{baseUrl}}/publicbiz/agency/list
Content-Type: application/json

### 2. 根据关键词查询机构列表
GET {{baseUrl}}/publicbiz/agency/list?keyword=测试
Content-Type: application/json

### 3. 根据合作状态查询机构列表
GET {{baseUrl}}/publicbiz/agency/list?cooperationStatus=cooperating
Content-Type: application/json

### 4. 根据审核状态查询机构列表
GET {{baseUrl}}/publicbiz/agency/list?reviewStatus=pending
Content-Type: application/json

### 5. 根据区县查询机构列表
GET {{baseUrl}}/publicbiz/agency/list?district=高新区
Content-Type: application/json

### 6. 根据机构编码查询机构列表
GET {{baseUrl}}/publicbiz/agency/list?agencyNo=AG001
Content-Type: application/json

### 7. 组合条件查询机构列表
GET {{baseUrl}}/publicbiz/agency/list?keyword=测试&cooperationStatus=cooperating&reviewStatus=pending
Content-Type: application/json

### 对比：原有的分页查询接口
GET {{baseUrl}}/publicbiz/agency/page?pageNum=1&pageSize=10
Content-Type: application/json
